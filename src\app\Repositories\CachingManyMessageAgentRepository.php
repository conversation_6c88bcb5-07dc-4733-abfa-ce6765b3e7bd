<?php

namespace App\Repositories;

use App\Models\ManymessageAgent;
use App\Repositories\Interfaces\ManyMessageAgentRepositoryInterface;
use Illuminate\Support\Facades\Cache;

class CachingManyMessageAgentRepository implements ManyMessageAgentRepositoryInterface
{
    private ManyMessageAgentRepositoryInterface $repo;

    public function __construct(
        ManyMessageAgentRepositoryInterface $repo
    ) {
        $this->repo = $repo;
    }

    /**
     * Find a ManyMessage agent by Instagram ID with caching
     *
     * @param string $instaId
     * @return ManymessageAgent|null
     */
    public function findByInstaId(string $instaId): ?ManymessageAgent
    {
        return Cache::remember(
            "manymessage_agent_insta_{$instaId}",
            now()->addMinutes(10),
            function () use ($instaId) {
                return $this->repo->findByInstaId($instaId);
            }
        );
    }

    /**
     * Create or update a ManyMessage agent and clear cache
     *
     * @param array $data
     * @return ManymessageAgent
     */
    public function updateOrCreate(array $data): ManymessageAgent
    {
        $agent = $this->repo->updateOrCreate($data);

        // Clear cache for this agent
        if ($agent->insta_id) {
            Cache::forget("manymessage_agent_insta_{$agent->insta_id}");
        }

        return $agent;
    }
}
