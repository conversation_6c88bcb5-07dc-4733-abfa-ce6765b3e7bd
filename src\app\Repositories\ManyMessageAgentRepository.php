<?php

namespace App\Repositories;

use App\Models\ManymessageAgent;
use App\Repositories\Interfaces\ManyMessageAgentRepositoryInterface;

class ManyMessageAgentRepository implements ManyMessageAgentRepositoryInterface
{
    /**
     * Find a ManyMessage agent by Instagram ID
     *
     * @param string $instaId
     * @return ManymessageAgent|null
     */
    public function findByInstaId(string $instaId): ?ManymessageAgent
    {
        return ManymessageAgent::where('insta_id', $instaId)->first();
    }

    /**
     * Create or update a ManyMessage agent
     *
     * @param array $data
     * @return ManymessageAgent
     */
    public function updateOrCreate(array $data): ManymessageAgent
    {
        // Ensure we have the required fields for updateOrCreate
        $searchCriteria = [];
        $updateData = $data;

        // If insta_id is provided, use it as search criteria
        if (isset($data['insta_id'])) {
            $searchCriteria['insta_id'] = $data['insta_id'];
        }

        // If email is provided and no insta_id, use email as search criteria
        if (empty($searchCriteria) && isset($data['email'])) {
            $searchCriteria['email'] = $data['email'];
        }

        // If no search criteria, throw exception
        if (empty($searchCriteria)) {
            throw new \InvalidArgumentException('Either insta_id or email must be provided for updateOrCreate');
        }

        return ManymessageAgent::updateOrCreate($searchCriteria, $updateData);
    }
}
