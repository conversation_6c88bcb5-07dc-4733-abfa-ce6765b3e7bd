<?php

namespace App\Providers;

use App\Contracts\TokenDecryptorInterface;
use App\Repositories\CachingSpecialUserRepository;
use App\Repositories\Interfaces\ManyMessageAgentRepositoryInterface;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface;
use App\Repositories\ManyMessageAgentRepository;
use App\Repositories\SpecialUserRepository;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */

    public function register()
    {
        $this->app->bind(SpecialUserRepositoryInterface::class, function ($app) {
            $tokenDecryptor = $app->make(TokenDecryptorInterface::class);

            return new CachingSpecialUserRepository(
                new SpecialUserRepository(
                    $tokenDecryptor,
                )
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
