<?php

namespace App\Services\WebhookService;

use App\Contracts\HttpClientInterface;
use App\Factories\Http\HttpClientFactory;
use Illuminate\Support\Facades\Log;

class ManyMessageForwarder
{
    private HttpClientInterface $httpClient;

    public function __construct()
    {
        $this->httpClient = HttpClientFactory::forManyMessage();
    }

    /**
     * Check if ManyMessage forwarding is enabled and configured.
     */
    public function isEnabled(): bool
    {
        return !empty(config('services.manymessage.endpoint'));
    }

    /**
     * Check if the webhook should be forwarded to ManyMessage service.
     * Add your specific business logic here.
     */
    public function shouldForward(array $payload): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

       
        
        return true; // For now, forward all webhooks if service is enabled
    }

    /**
     * Forward the raw webhook payload to ManyMessage service.
     */
    public function forward(array $payload): bool
    {
        if (!$this->shouldForward($payload)) {
            return false;
        }

        try {
            $headers = [
                'Content-Type' => 'application/json',
                'User-Agent' => 'DMPlus-Webhook-Forwarder/1.0',
            ];

            $body = [
                'webhook_data' => $payload,
                'timestamp' => now()->toISOString(),
                'source' => 'dmplus-webservice',
            ];

            $response = $this->httpClient->post('', $headers, $body);

            if ($response->successful()) {
                Log::info('Webhook successfully forwarded to ManyMessage', [
                    'status' => $response->status(),
                    'payload_size' => strlen(json_encode($payload)),
                ]);
                return true;
            } else {
                Log::warning('Failed to forward webhook to ManyMessage', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'payload_size' => strlen(json_encode($payload)),
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Exception while forwarding webhook to ManyMessage', [
                'error' => $e->getMessage(),
                'payload_size' => strlen(json_encode($payload)),
            ]);
            return false;
        }
    }
}
