<?php

namespace App\Repositories\Interfaces;

use App\Models\ManymessageAgent;

interface ManyMessageAgentRepositoryInterface
{
    /**
     * Find a ManyMessage agent by Instagram ID
     *
     * @param string $instaId
     * @return ManymessageAgent|null
     */
    public function findByInstaId(string $instaId): ?ManymessageAgent;

    /**
     * Create or update a ManyMessage agent
     *
     * @param array $data
     * @return ManymessageAgent
     */
    public function updateOrCreate(array $data): ManymessageAgent;
}
