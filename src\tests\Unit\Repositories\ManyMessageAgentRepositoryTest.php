<?php

namespace Tests\Unit\Repositories;

use App\Models\ManymessageAgent;
use App\Repositories\ManyMessageAgentRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ManyMessageAgentRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ManyMessageAgentRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new ManyMessageAgentRepository();
    }

    public function test_find_by_insta_id_returns_agent_when_exists(): void
    {
        // Arrange
        $agent = ManymessageAgent::create([
            'email' => '<EMAIL>',
            'insta_id' => 'test_insta_123'
        ]);

        // Act
        $result = $this->repository->findByInstaId('test_insta_123');

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($agent->id, $result->id);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertEquals('test_insta_123', $result->insta_id);
    }

    public function test_find_by_insta_id_returns_null_when_not_exists(): void
    {
        // Act
        $result = $this->repository->findByInstaId('non_existent_id');

        // Assert
        $this->assertNull($result);
    }

    public function test_update_or_create_creates_new_agent_with_insta_id(): void
    {
        // Act
        $result = $this->repository->updateOrCreate([
            'email' => '<EMAIL>',
            'insta_id' => 'new_insta_456'
        ]);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertEquals('new_insta_456', $result->insta_id);
        $this->assertDatabaseHas('manymessage_agents', [
            'email' => '<EMAIL>',
            'insta_id' => 'new_insta_456'
        ]);
    }

    public function test_update_or_create_updates_existing_agent_by_insta_id(): void
    {
        // Arrange
        $agent = ManymessageAgent::create([
            'email' => '<EMAIL>',
            'insta_id' => 'existing_insta_789'
        ]);

        // Act
        $result = $this->repository->updateOrCreate([
            'email' => '<EMAIL>',
            'insta_id' => 'existing_insta_789'
        ]);

        // Assert
        $this->assertEquals($agent->id, $result->id);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertEquals('existing_insta_789', $result->insta_id);
        $this->assertDatabaseHas('manymessage_agents', [
            'id' => $agent->id,
            'email' => '<EMAIL>',
            'insta_id' => 'existing_insta_789'
        ]);
    }

    public function test_update_or_create_creates_new_agent_with_email_only(): void
    {
        // Act
        $result = $this->repository->updateOrCreate([
            'email' => '<EMAIL>'
        ]);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertNull($result->insta_id);
        $this->assertDatabaseHas('manymessage_agents', [
            'email' => '<EMAIL>',
            'insta_id' => null
        ]);
    }

    public function test_update_or_create_throws_exception_when_no_search_criteria(): void
    {
        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Either insta_id or email must be provided for updateOrCreate');

        // Act
        $this->repository->updateOrCreate([]);
    }
}
